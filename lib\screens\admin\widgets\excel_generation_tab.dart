import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../providers/providers.dart';
import '../../../models/models.dart';
import '../../../services/android_download_service.dart';
import '../../../services/excel_generator_service.dart';
import '../../../services/reports_service.dart';

/// Onglet pour la génération de rapports Excel uniquement
class ExcelGenerationTab extends StatefulWidget {
  const ExcelGenerationTab({super.key});

  @override
  State<ExcelGenerationTab> createState() => _ExcelGenerationTabState();
}

class _ExcelGenerationTabState extends State<ExcelGenerationTab> {
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 7));
  DateTime _endDate = DateTime.now();
  int? _selectedEmployeeId;
  int? _selectedSiteId;
  String _reportType = 'all_employees';
  bool _isGenerating = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildReportTypeSelector(),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildDateSelector(),
            const SizedBox(height: AppConstants.defaultPadding),
            if (_reportType == 'individual') _buildEmployeeSelector(),
            if (_reportType == 'site') _buildSiteSelector(),
            const SizedBox(height: AppConstants.largePadding),
            _buildGenerateButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.buttonText, AppColors.buttonText.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.background.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.table_chart,
              color: AppColors.textPrimary,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إنشاء تقارير الحضور',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'إنشاء وتحميل تقارير الحضور بصيغة CSV (متوافق مع Excel)',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppColors.textPrimary.withOpacity(0.9),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportTypeSelector() {
    return Card(
      elevation: 2,
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نوع التقرير',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: 12),
            Column(
              children: [
                RadioListTile<String>(
                  title: const Text(
                    'جميع الموظفين',
                    style: TextStyle(color: AppColors.textPrimary),
                  ),
                  subtitle: const Text(
                    'تقرير شامل لجميع الموظفين',
                    style: TextStyle(color: AppColors.textPrimary),
                  ),
                  value: 'all_employees',
                  groupValue: _reportType,
                  onChanged: (value) {
                    setState(() {
                      _reportType = value ?? 'all_employees';
                      _selectedEmployeeId = null;
                      _selectedSiteId = null;
                    });
                  },
                  activeColor: AppColors.buttonText,
                ),
                RadioListTile<String>(
                  title: const Text(
                    'موظف محدد',
                    style: TextStyle(color: AppColors.textPrimary),
                  ),
                  subtitle: const Text(
                    'تقرير مفصل لموظف واحد',
                    style: TextStyle(color: AppColors.textPrimary),
                  ),
                  value: 'individual',
                  groupValue: _reportType,
                  onChanged: (value) {
                    setState(() {
                      _reportType = value ?? 'all_employees';
                      _selectedSiteId = null;
                    });
                  },
                  activeColor: AppColors.buttonText,
                ),
                RadioListTile<String>(
                  title: const Text(
                    'موقع محدد',
                    style: TextStyle(color: AppColors.textPrimary),
                  ),
                  subtitle: const Text(
                    'تقرير لجميع موظفي موقع معين',
                    style: TextStyle(color: AppColors.textPrimary),
                  ),
                  value: 'site',
                  groupValue: _reportType,
                  onChanged: (value) {
                    setState(() {
                      _reportType = value ?? 'all_employees';
                      _selectedEmployeeId = null;
                    });
                  },
                  activeColor: AppColors.buttonText,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector() {
    return Card(
      elevation: 2,
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'فترة التقرير',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateField(
                    label: 'من تاريخ',
                    date: _startDate,
                    onTap: () => _selectStartDate(),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateField(
                    label: 'إلى تاريخ',
                    date: _endDate,
                    onTap: () => _selectEndDate(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildQuickDateButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.buttonText.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.buttonText,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: AppColors.buttonText,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    DateFormat('dd/MM/yyyy').format(date),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickDateButtons() {
    return Wrap(
      spacing: 6,
      runSpacing: 8,
      alignment: WrapAlignment.start,
      children: [
        _buildQuickDateButton('اليوم', () {
          setState(() {
            _startDate = DateTime.now();
            _endDate = DateTime.now();
          });
        }),
        _buildQuickDateButton('أمس', () {
          final yesterday = DateTime.now().subtract(const Duration(days: 1));
          setState(() {
            _startDate = yesterday;
            _endDate = yesterday;
          });
        }),
        _buildQuickDateButton('آخر 7 أيام', () {
          setState(() {
            _startDate = DateTime.now().subtract(const Duration(days: 7));
            _endDate = DateTime.now();
          });
        }),
        _buildQuickDateButton('آخر 30 يوم', () {
          setState(() {
            _startDate = DateTime.now().subtract(const Duration(days: 30));
            _endDate = DateTime.now();
          });
        }),
      ],
    );
  }

  Widget _buildQuickDateButton(String label, VoidCallback onPressed) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: AppColors.buttonText),
        foregroundColor: AppColors.buttonText,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        minimumSize: const Size(0, 36),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Text(
        label,
        style: const TextStyle(color: AppColors.textPrimary, fontSize: 13),
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }

  Widget _buildEmployeeSelector() {
    return Card(
      elevation: 2,
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار الموظف',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: 12),
            Consumer<EmployeesProvider>(
              builder: (context, employeesProvider, child) {
                if (employeesProvider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: AppColors.buttonText,
                    ),
                  );
                }

                return DropdownButtonFormField<int>(
                  value: _selectedEmployeeId,
                  decoration: InputDecoration(
                    labelText: 'اختر الموظف',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(
                      Icons.person,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  items: employeesProvider.employees.map((employee) {
                    return DropdownMenuItem<int>(
                      value: employee.id,
                      child: Text(
                        employee.name,
                        style: const TextStyle(color: AppColors.textPrimary),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedEmployeeId = value;
                    });
                  },
                  dropdownColor: AppColors.background,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSiteSelector() {
    return Card(
      elevation: 2,
      color: AppColors.background,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختيار الموقع',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.buttonText,
              ),
            ),
            const SizedBox(height: 12),
            Consumer<SitesProvider>(
              builder: (context, sitesProvider, child) {
                if (sitesProvider.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(
                      color: AppColors.buttonText,
                    ),
                  );
                }

                return DropdownButtonFormField<int>(
                  value: _selectedSiteId,
                  decoration: InputDecoration(
                    labelText: 'اختر الموقع',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(
                      Icons.location_on,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  items: sitesProvider.sites.map((site) {
                    return DropdownMenuItem<int>(
                      value: site.id,
                      child: Text(
                        site.name,
                        style: const TextStyle(color: AppColors.textPrimary),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedSiteId = value;
                    });
                  },
                  dropdownColor: AppColors.background,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _isGenerating ? null : _generateExcelReport,
        icon: _isGenerating
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    AppColors.textPrimary,
                  ),
                ),
              )
            : Icon(Icons.table_chart, size: 24, color: AppColors.textPrimary),
        label: Text(
          _isGenerating ? 'جاري إنشاء التقرير...' : 'إنشاء تقرير CSV',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.buttonBackground,
          foregroundColor: AppColors.buttonText,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 3,
        ),
      ),
    );
  }

  Future<void> _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.buttonText,
              onPrimary: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (date != null) {
      setState(() {
        _startDate = date;
        if (_startDate.isAfter(_endDate)) {
          _endDate = _startDate;
        }
      });
    }
  }

  Future<void> _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _endDate,
      firstDate: _startDate,
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.buttonText,
              onPrimary: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );
    if (date != null) {
      setState(() {
        _endDate = date;
      });
    }
  }

  Future<void> _generateExcelReport() async {
    if (!_validateForm()) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      final reportsProvider = context.read<ReportsProvider>();

      // Définir les dates dans le provider
      reportsProvider.setDateRange(_startDate, _endDate);

      // Générer le rapport selon le type
      bool success = false;
      switch (_reportType) {
        case 'all_employees':
          success = await reportsProvider.generateEmployeeReport();
          break;
        case 'individual':
          if (_selectedEmployeeId != null) {
            success = await reportsProvider.generateIndividualReport(
              _selectedEmployeeId!,
            );
          }
          break;
        case 'site':
          if (_selectedSiteId != null) {
            success = await reportsProvider.generateSiteReport(
              _selectedSiteId!,
            );
          }
          break;
      }

      if (success && mounted) {
        // Afficher un message de succès
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إنشاء التقرير بنجاح! سيتم تحميله تلقائياً بصيغة Excel',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: AppColors.buttonText,
            duration: Duration(seconds: 3),
          ),
        );

        // Télécharger et sauvegarder le fichier Excel réel
        await _downloadRealExcelReport();
      } else if (!success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في إنشاء التقرير',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في إنشاء التقرير: $e',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isGenerating = false;
        });
      }
    }
  }

  Future<void> _downloadRealExcelReport() async {
    try {
      debugPrint(
        'ExcelGenerationTab: Starting real Excel download process with database data',
      );

      // Utiliser la nouvelle méthode qui récupère les vraies données de la base de données
      final result = await ReportsService().generateAndDownloadCSVReport(
        reportType: _reportType,
        startDate: _startDate,
        endDate: _endDate,
        employeeId: _selectedEmployeeId,
        siteId: _selectedSiteId,
      );

      if (mounted &&
          result != null &&
          result.success &&
          result.filePath != null) {
        // عرض رسالة نجاح مع خيارات المشاركة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم إنشاء وحفظ تقرير CSV بنجاح في مجلد التحميلات',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: AppColors.buttonText,
            duration: Duration(seconds: 3),
          ),
        );

        // عرض خيارات المشاركة
        _showExcelShareOptions(result);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في حفظ الملف في مجلد التحميلات',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    } catch (e) {
      debugPrint('ExcelGenerationTab: Error in real Excel download: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تحميل التقرير: $e',
              style: TextStyle(color: AppColors.background),
            ),
            backgroundColor: AppColors.buttonText,
          ),
        );
      }
    }
  }

  Future<String?> _saveRealExcelFile(String filename, Uint8List data) async {
    try {
      debugPrint('ExcelGenerationTab: Saving real Excel file: $filename');

      // Utiliser le service Android pour sauvegarder le fichier
      final androidDownloadService = AndroidDownloadService();
      final filePath = await AndroidDownloadService.saveAndOpenExcelFile(
        filename,
        data,
      );

      if (filePath != null) {
        debugPrint('ExcelGenerationTab: File saved successfully at: $filePath');
        return filePath;
      } else {
        throw Exception('Failed to save Excel file');
      }
    } catch (e) {
      debugPrint('ExcelGenerationTab: Error saving Excel file: $e');
      rethrow;
    }
  }

  void _showExcelShareOptions(DownloadResult result) {
    if (result.filePath == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.background,
        title: Row(
          children: [
            Icon(Icons.table_chart, color: AppColors.buttonText),
            SizedBox(width: 8),
            Text(
              'تقرير CSV جاهز',
              style: TextStyle(color: AppColors.textPrimary),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.buttonBackground,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.buttonText.withOpacity(0.3),
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: AppColors.buttonText,
                    size: 32,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'تم إنشاء وحفظ التقرير بنجاح',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 4),
                  Text(
                    result.filename,
                    style: TextStyle(color: AppColors.buttonText),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),
            Text(
              'يمكنك الآن مشاركة التقرير أو العثور عليه في مجلد التحميلات',
              textAlign: TextAlign.center,
              style: TextStyle(color: AppColors.textPrimary),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'إغلاق',
              style: TextStyle(color: AppColors.buttonText),
            ),
          ),
          TextButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              final reportsProvider = context.read<ReportsProvider>();
              await reportsProvider.shareViaEmail(
                result.filePath!,
                result.filename,
              );
            },
            icon: Icon(Icons.email, size: 16, color: AppColors.buttonText),
            label: const Text(
              'إيميل',
              style: TextStyle(color: AppColors.buttonText),
            ),
          ),
          TextButton.icon(
            onPressed: () async {
              Navigator.of(context).pop();
              final reportsProvider = context.read<ReportsProvider>();
              await reportsProvider.shareViaWhatsApp(
                result.filePath!,
                result.filename,
              );
            },
            icon: Icon(Icons.chat, size: 16, color: AppColors.buttonText),
            label: const Text(
              'واتساب',
              style: TextStyle(color: AppColors.buttonText),
            ),
          ),
        ],
      ),
    );
  }

  bool _validateForm() {
    if (_reportType == 'individual' && _selectedEmployeeId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'يرجى اختيار موظف',
            style: TextStyle(color: AppColors.background),
          ),
          backgroundColor: AppColors.buttonText,
        ),
      );
      return false;
    }

    if (_reportType == 'site' && _selectedSiteId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'يرجى اختيار موقع',
            style: TextStyle(color: AppColors.background),
          ),
          backgroundColor: AppColors.buttonText,
        ),
      );
      return false;
    }

    if (_startDate.isAfter(_endDate)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
            'تاريخ البداية يجب أن يكون قبل تاريخ النهاية',
            style: TextStyle(color: AppColors.background),
          ),
          backgroundColor: AppColors.buttonText,
        ),
      );
      return false;
    }

    return true;
  }
}
